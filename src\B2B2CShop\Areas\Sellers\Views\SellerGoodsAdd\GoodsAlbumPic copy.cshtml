﻿@using B2B2CShop.Entity
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    var storeEntity = Store.FindByUId(ManageProvider.User?.ID??0);
    var goodsAlbum = AlbumCategory.FindAllByStoreId(storeEntity?.Id??0);
}

<div class="goods-gallery" dstype="gallery-0">
    <a class='sample_demo' id='@Model.submitName' href="@Url.Action("Piclistmultiple")" style="display:none;">提交</a>
    <div class="nav">
        <span class="l">
            用户相册 >
            全部图片
        </span>
        <span class="r">
            <select name="jumpMenu" id="jumpMenu" style="width:100px;">
                <option value="0" style="width:80px;">-请选择-</option>
                @{
                    foreach (var item in goodsAlbum)
                    {
                        <option style="width:80px;" value="@item.Id" selected="@item.IsDefault">@item.Name</option>
                    }
                }
            </select>
        </span>
    </div>
    <ul class="list">
        @* <li onclick="insert_img('1_2025031114262050232.png', 'http://b2b2c.h.com/uploads/home/<USER>/goods/1/20250311/1_2025031114262050232_240.png');">
            <a href="JavaScript:void(0);">
                <img src="http://b2b2c.h.com/uploads/home/<USER>/goods/1/20250311/1_2025031114262050232_240.png" />
            </a>
        </li> *@
    </ul>
    <div class="pagination">
        <ul class="pagination"> 
            <li class="disabled">
                <span></span>
            </li> 
            <li class="disabled">
                <span>首页</span>
            </li> 
            <li class="disabled">
                <span>«</span>
            </li> <li class="active">
                <span>1</span>
            </li>
            <li>
                <a href="/home/<USER>/pic_list.html?item=goods&amp;page=2">2</a>
            </li>
            <li>
                <a href="/home/<USER>/pic_list.html?item=goods&amp;page=3">3</a>
            </li>
            <li>
                <a href="/home/<USER>/pic_list.html?item=goods&amp;page=4">4</a>
            </li>
            <li>
                <a href="/home/<USER>/pic_list.html?item=goods&amp;page=5">5</a>
            </li> 
            <li>
                <a href="/home/<USER>/pic_list.html?item=goods&amp;page=2">»</a>
            </li> 
            <li>
               <a href="/home/<USER>/pic_list.html?item=goods&amp;page=10">尾页</a>
            </li> 
            <li>
                <div style='display:flex;margin-left:2px;padding:0'>
                    <span style='color: #777;'>到</span>
                    <input style='height:36px;padding:0;border:1px solid #e6e6e6; display: inline-block; width: 40px; text-align: center; margin:0px 2px;color: #777;' type='text' name='page' value='1' class='form-control'> 
                    <span style='color: #777;'>页</span> 
                    <a href=/home/<USER>/pic_list.html?item =goods class='btn' id='pagination_gourl' style='height:20px;border-radius:0;'>确定</a>
                </div>
            </li>
        </ul>
    </div>
</div>
<script>
    $(document).ready(function(){
        var defaultAlbumId = $('#jumpMenu option:selected').val();
        if(defaultAlbumId != '0') {
            loadAlbumPics(defaultAlbumId);
        }
        function loadAlbumPics(aId,page = 1,limit = 12){
             $.ajax({
                url: '/Sellers/SellerGoodsAdd/GetAlnumPic',
                type: 'GET',
                data: {
                    aId: aId,
                    page: page,
                    limit: limit
                },
                success: function(result) {
                    if(result.success) {
                        // 清空并重新渲染图片列表
                        $('.goods-gallery .list').empty();
                        if(result.data) {
                            result.data.forEach(function(item) {
                                var li = $('<li>').attr('onclick','insert_img("' + item.Name + '", "' + item.Cover + '")');
                                if(@Model.editdemo==1) 
                                {
                                     li = $('<li>').attr('onclick','insert_editor("' + item.Cover + '")');
                                }
                                else if(@Model.editdemo==2)
                                {
                                         li = $('<li>').attr('onclick','insert_img("' + item.Name + '", "' + item.Cover + '",0)');
                                }
                                var a = $('<a>').attr('href', 'JavaScript:void(0);');
                                var img = $('<img>').attr('src', item.Cover);
                                
                                li.append(a.append(img));
                                $('.goods-gallery .list').append(li);
                            });
                             // 更新分页信息
                            var totalCount = result.extdata.TotalCount || 0;
                            var totalPages = Math.ceil(totalCount / limit);
                            var pageInfo = `共${totalCount}条记录 第${page}页/共${totalPages}页`;
                            $('.pagination li:first span').text(pageInfo);

                            // 更新分页链接
                            updatePagination(page, totalPages, aId);
                        }
                    } else {
                        alert(result.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('获取图片数据失败:', error);
                }
            });
        }
        // 更新分页链接
function updatePagination(currentPage, totalPages, aId) {
    var $pagination = $('.pagination');
    var $pageLinks = $pagination.find('li:not(:first):not(:last)');
    
    // 清空现有页码链接
    $pageLinks.empty();
    
    // 添加首页
    $pageLinks.first().html(`<a href="javascript:void(0);" data-page="1">首页</a>`);
    
    // 添加上一页
    if(currentPage > 1) {
        $pageLinks.eq(1).html(`<a href="javascript:void(0);" data-page="${currentPage-1}">«</a>`);
    } else {
        $pageLinks.eq(1).html(`<span>«</span>`);
    }
    
    // 添加页码
    for(let i = 1; i <= totalPages && i <= 5; i++) {
        let isActive = i === currentPage;
        let pageHtml = isActive ? 
            `<span>${i}</span>` : 
            `<a href="javascript:void(0);" data-page="${i}">${i}</a>`;
        $pageLinks.eq(i+1).html(pageHtml);
    }
    
    // 添加下一页
    if(currentPage < totalPages) {
        $pageLinks.eq(-2).html(`<a href="javascript:void(0);" data-page="${currentPage+1}">»</a>`);
    } else {
        $pageLinks.eq(-2).html(`<span>»</span>`);
    }
    
    // 添加尾页
    $pageLinks.last().html(`<a href="javascript:void(0);" data-page="${totalPages}">尾页</a>`);
    
    // 更新当前页输入框
    $('input[name="page"]').val(currentPage);
}
        // 分类切换事件
        $('#jumpMenu').change(function(){
            var aId = $(this).val();
            console.log("aId:"+aId);
            if(aId != '0') {
                loadAlbumPics(aId);
            }
        });

        // 分页点击事件
        $(document).on('click', 'ul.pagination>li>a', function(e){
            e.preventDefault();
            var aId = $('#jumpMenu').val();
            if(aId != '0') {
                var page = $(this).data('page') || 1;
                loadAlbumPics(aId, page);
            }
        });
        // 跳转页面事件
        $('#pagination_gourl').click(function(e){
            e.preventDefault();
            var aId = $('#jumpMenu').val();
            if(aId != '0') {
                var page = $('input[name="page"]').val();
                loadAlbumPics(aId, page);
            }
        });
    });
    $(document).ready(function(){
	$('ul.pagination>li>a').ajaxContent({
		event:'click', //mouseover
		loaderType:'img',
		loadingMsg:'/static/home/<USER>/loading.gif',
		target:'#demo'
	});
	$('#jumpMenu').change(function(){
		$('#select_submit').attr('href',$('#select_submit').attr('href')+"&id="+$('#jumpMenu').val());
		$('#select_submit').ajaxContent({
			event:'click', //mouseover
			loaderType:'img',
			loadingMsg:'/static/home/<USER>/loading.gif',
			target:'#demo'
		});
		$('#select_submit').click();
	});
});
</script>

